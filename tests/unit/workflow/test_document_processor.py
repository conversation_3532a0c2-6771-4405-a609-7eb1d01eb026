import sys
from pathlib import Path
import shutil
import subprocess
import yaml
import os
import tempfile

import pytest

# Ensure repo root is importable
ROOT = Path(__file__).resolve().parents[4]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

from workflow.document_processor import shard_documents, validate_document_structure, ShardingResult

TEST_PRD = """# Title

## Section A
Content A

## Section B
Content B
"""

def write_core_config(tmpdir: Path, enable_exploder: bool):
    core = tmpdir / ".bmad-core"
    core.mkdir(exist_ok=True)
    cfg = {"markdownExploder": enable_exploder}
    (core / "core-config.yaml").write_text(yaml.safe_dump(cfg), encoding="utf-8")
    return core / "core-config.yaml"

def write_sample_prd(tmpdir: Path) -> Path:
    docs = tmpdir / "docs"
    docs.mkdir(exist_ok=True)
    prd = docs / "prd.md"
    prd.write_text(TEST_PRD, encoding="utf-8")
    return prd

def test_shard_documents_fallback_when_md_tree_missing(tmp_path, monkeypatch):
    """
    When md-tree is not in PATH or markdownExploder is false, shard_documents should
    fall back to built-in splitting and produce files under docs/prd/.
    """
    project_root = tmp_path
    # create repo layout and prd.md
    prd = write_sample_prd(project_root)
    # create .bmad-core config disabling markdownExploder
    write_core_config(project_root, enable_exploder=False)

    # Ensure md-tree is not found
    monkeypatch.setattr(shutil, "which", lambda name: None)

    # Run shard_documents
    res: ShardingResult = shard_documents(str(prd))

    assert res.success is True
    assert res.index_file is not None
    # check generated files list and that they exist
    assert any("intro.md" in p or "section-a.md" in p.lower() or "section-b.md" in p.lower() for p in res.sharded_files)
    for p in res.sharded_files:
        assert Path(p).exists()
        v = validate_document_structure(p)
        assert v.valid is True

def test_shard_documents_uses_md_tree_when_enabled_and_available(tmp_path, monkeypatch):
    """
    When markdownExploder is true and md-tree exists and returns success,
    shard_documents should return the ShardingResult produced by md-tree.
    We simulate md-tree by having it write expected files into the output dir.
    """
    project_root = tmp_path
    prd = write_sample_prd(project_root)
    cfg_path = write_core_config(project_root, enable_exploder=True)

    # Create a fake md-tree in a temporary bin dir and ensure it's used
    fake_bin = tmp_path / "bin"
    fake_bin.mkdir()
    fake_md_tree = fake_bin / "md-tree"
    # Create a dummy executable script that will simulate md-tree explode by creating files
    fake_md_tree.write_text("#!/usr/bin/env python3\nimport sys\nfrom pathlib import Path\ninfile = Path(sys.argv[2])\noutdir = Path(sys.argv[3])\noutdir.mkdir(parents=True, exist_ok=True)\n# create two md files and an index\n(outdir / 'a.md').write_text('# A\\n\\ncontent')\n(outdir / 'b.md').write_text('# B\\n\\ncontent')\n(outdir / 'index.md').write_text('# Index\\n- [A](a.md)\\n- [B](b.md)')\n", encoding="utf-8")
    fake_md_tree.chmod(0o755)

    # monkeypatch PATH lookup to find our fake md-tree
    monkeypatch.setattr(shutil, "which", lambda name: str(fake_md_tree) if name == "md-tree" else None)

    # Also monkeypatch subprocess.run to call the real fake_md_tree script (it exists), so no need to mock run.
    # However ensure cwd is project root when calling shard_documents by running from that dir: shard_documents uses repo root detection.

    # Run shard_documents; since md-tree is "available" and config enables it, it should be used.
    res: ShardingResult = shard_documents(str(prd))

    assert res.success is True
    # md-tree should have created index.md in docs/prd/
    assert res.index_file is not None
    assert Path(res.index_file).exists()
    # there should be at least the two files we created
    assert any(p.endswith("a.md") or p.endswith("b.md") for p in res.sharded_files)

def test_validate_document_structure_detects_missing_file(tmp_path):
    p = tmp_path / "missing.md"
    res = validate_document_structure(str(p))
    assert res.valid is False
    assert any("not found" in issue.lower() for issue in res.issues)